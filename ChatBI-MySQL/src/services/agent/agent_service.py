"""
Agent service module.

This module provides utilities for managing and retrieving agent configurations.
It centralizes agent-related operations to reduce code duplication.
"""

from typing import List, Dict, Any
import yaml

from src.utils.logger import logger
from src.utils.resource_manager import list_resources, load_resource


def get_available_agents() -> List[Dict[str, Any]]:
    """
    Get all available agent configurations.
    
    This function loads agent configurations from the data_fetcher_bot_config directory
    and returns a list of agent information including names and descriptions.
    
    Returns:
        List[Dict[str, Any]]: A list of agent configurations with the following structure:
            [
                {
                    "agent_name": "agent_name",
                    "description": "agent_description",
                    "config": {...}  # Full config if needed
                },
                ...
            ]
    """
    agents = []
    
    try:
        # Get all YAML files from the data_fetcher_bot_config directory
        config_files = list_resources('data_fetcher_bot_config', '.yml')
        
        for config_file in config_files:
            # Load the configuration from the YAML file
            yaml_content = load_resource('data_fetcher_bot_config', config_file)
            if yaml_content:
                try:
                    config = yaml.safe_load(yaml_content)
                    if config and 'agent_name' in config:
                        # Add _specialist suffix to match the actual agent names used in the system
                        base_agent_name = config.get('agent_name')
                        actual_agent_name = f"{base_agent_name}_specialist"

                        # 获取描述内容：优先使用system_prompt，其次使用agent_description
                        description = ""
                        system_prompt_file = config.get('system_prompt')
                        if system_prompt_file:
                            description = load_resource("prompt", system_prompt_file) or ""
                        else:
                            description = config.get('agent_description', '')

                        agent_info = {
                            "agent_name": actual_agent_name,
                            "base_name": base_agent_name,
                            "description": description,
                            "config_file": config_file
                        }
                        # Only include the full config if needed (commented out for now)
                        # agent_info["config"] = config
                        agents.append(agent_info)
                        logger.debug(f"Loaded agent config: {agent_info['agent_name']}")
                except Exception as e:
                    logger.error(f"Error parsing YAML config file {config_file}: {e}")
        
        logger.info(f"Successfully loaded {len(agents)} agent configurations")
        return agents

    except Exception as e:
        logger.error(f"Error loading agent configurations: {e}", exc_info=True)
        # Return empty list if loading fails
        return []


def get_agent_names() -> List[str]:
    """
    Get a list of all available agent names.
    
    Returns:
        List[str]: A list of agent names
    """
    agents = get_available_agents()
    return [agent['agent_name'] for agent in agents]


def get_agent_by_name(agent_name: str) -> Dict[str, Any]:
    """
    Get agent configuration by name.
    
    Args:
        agent_name (str): The name of the agent to retrieve
        
    Returns:
        Dict[str, Any]: Agent configuration or None if not found
    """
    agents = get_available_agents()
    for agent in agents:
        if agent['agent_name'] == agent_name:
            return agent
    return None
