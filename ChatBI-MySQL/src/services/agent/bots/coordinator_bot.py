"""
协调者Bot实现 - Agent as Tool架构

这个Bot作为主协调者，负责：
1. 接收用户查询并保持对话主体地位
2. 分析查询内容，决定调用哪些专业Agent工具
3. 协调多个专业Agent的执行（支持并行）
4. 聚合和整理专业Agent的结果
5. 统一错误处理和重试机制
"""

from typing import Optional, Dict, Any, List
from agents import Agent, Model, ItemHelpers, MessageOutputItem, ToolCallOutputItem
from agents.items import ToolCallItem
from agents.result import RunResult
from openai.types.responses.response_function_tool_call import ResponseFunctionToolCall
from src.services.agent.tools.tool_manager import tool_manager
from src.services.agent.bots.data_fetcher_bot import DataFetcherBot
from src.utils.logger import logger
from datetime import datetime


class CoordinatorBot(DataFetcherBot):
    """
    协调者Bot - Agent as Tool架构的核心

    负责协调多个专业Agent工具，实现统一的用户交互界面。
    继承DataFetcherBot以使用配置文件驱动的架构：
    - 使用YAML配置文件定义工具和模型设置
    - 支持商品搜索工具
    - 动态添加专业Agent工具
    """

    def __init__(
        self, user_info: Dict[str, Any], config_file: str = "coordinator_bot.yml"
    ):
        # 调用父类构造函数，加载配置文件
        super().__init__(user_info, config_file)
        self.log_items:List[str]=[]

    def get_description(self) -> str:
        return "我是鲜沐ChatBI智能助手，能够分析您的问题并调用最合适的专业分析工具来为您提供准确的答案。我可以处理销售分析、仓储物流、知识查询等多个领域的问题，并能搜索商品信息与用户确认。"

    def get_known_agents(self) -> set:
        """
        获取当前配置的所有可用agent名称

        Returns:
            set: 包含所有可用agent名称的集合
        """
        try:
            agent_tools = self.config.get("agent_tools", [])
            agent_names = {tool.get("name") for tool in agent_tools if tool.get("name")}
            logger.debug(f"CoordinatorBot配置的agent名称: {agent_names}")
            return agent_names
        except Exception as e:
            logger.warning(f"获取CoordinatorBot的agent名称时出错: {e}")
            return set()

    def create_agent(
        self, model: Optional[Model] = None, model_settings_str: Optional[str] = None
    ) -> Agent:
        """
        创建协调者Agent实例，重写父类方法以添加专业Agent工具

        Args:
            model: 使用的模型实例
            model_settings_str: 模型设置字符串

        Returns:
            Agent: 配置好的协调者Agent
        """
        # 调用父类方法获取基础Agent配置
        agent_name = self.config.get("agent_name", "coordinator_bot")
        tools = self.config.get("tools", [])
        agent_tools = self.config.get("agent_tools", [])

        # 获取描述内容：使用新的加载机制
        agent_description = self._load_agent_description()

        # 从配置文件读取model配置
        config_model = self.config.get("model")
        config_model_settings = self.get_model_settings()

        logger.info(
            f"协调者Bot配置: agent_name={agent_name}, tools={tools}, config_model={config_model}"
        )

        async def _custom_output_extractor(run_result:RunResult) -> str:
            final_output=""
            try:
                last_args=""
                for item in run_result.new_items:
                    logger.info(f"item: {item.type}")
                    time_stamp=datetime.now().strftime("%H:%M:%S")
                    if isinstance(item, MessageOutputItem):
                        text = ItemHelpers.text_message_output(item)
                        self.log_items.append(f"[{time_stamp}]: {item.type}: {text}")
                        final_output+=f"{text}\n"
                    if isinstance(item, ToolCallItem):
                        logger.info(f"ToolCallItem: {item.raw_item}")
                        args = item.raw_item.arguments if hasattr(item.raw_item, 'arguments') else ''
                        last_args=args
                        tool_name = item.raw_item.name if hasattr(item.raw_item, 'name') else ''
                        self.log_items.append(f"[{time_stamp}]: Called tool:{tool_name} with args: {args}")
                        final_output+=f"{tool_name} called with args: {args}\n"
                    if isinstance(item, ToolCallOutputItem):
                        tool_output=f"{item.output if hasattr(item, 'output') else item}"
                        if "create table" in tool_output.lower():
                            logger.info(f"DDL文件内容: {tool_output[:100]}, 将被替换为AI读取了DDL文件")
                            tool_output=f"AI读取了以下表的DDL内容:{last_args}"
                            last_args=""
                        self.log_items.append(f"[{time_stamp}]: {tool_output}")
                        final_output+=f"{tool_output}\n"
            except Exception as e:
                logger.exception(f"unable to extract output: {e}")
                return f"{e}"
            return final_output

        # 获取配置文件中定义的工具（如商品搜索工具）
        config_tool_list = tool_manager.get_tool_list([tool["name"] for tool in tools])
        if agent_tools:
            logger.info(f"协调者Bot配置的专业Agent工具数量: {len(agent_tools)}")
            for agent_tool in agent_tools:
                bot = DataFetcherBot(self.user_info, agent_tool.get("name") + ".yml")
                agent_as_tool = bot.create_agent().as_tool(
                    tool_name=agent_tool.get("name"),
                    tool_description=bot.get_agent_as_tool_description(),
                    custom_output_extractor=_custom_output_extractor,
                )
                config_tool_list.append(agent_as_tool)
                logger.info(f"添加专业Agent工具: {agent_tool.get('name')}")

        # 使用配置文件中的agent_description作为指令
        instruction = agent_description

        # 确定使用的模型
        final_model = self._safe_get_model(config_model, model)

        # 处理model_settings
        model_settings = self._safe_get_model_settings(
            config_model_settings, model_settings_str
        )

        logger.info(
            f"协调者Bot工具数量: 配置工具={len(config_tool_list)}, 专业Agent工具={len(agent_tools)}, 总计={len(config_tool_list) + len(agent_tools)}"
        )

        # 创建Agent实例
        agent_kwargs = {
            "name": f"{agent_name}",
            "instructions": instruction,
            "model": final_model,
            "tools": config_tool_list,
        }

        if model_settings:
            agent_kwargs["model_settings"] = model_settings

        return Agent(**agent_kwargs)

    def get_user_realtime_instruction(self) -> str:
        """
        重写父类方法，为协调者Agent定制实时指令
        """
        # 获取基础的用户信息，但不包含SQL相关的指令
        from datetime import datetime
        import textwrap
        from src.services.agent.utils.permissions import get_user_permission

        try:
            date_time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            data_permission_markup = get_user_permission(self.user_info)

            # 为协调者定制的实时指令
            coordinator_instruction = textwrap.dedent(
                f"""
                ## 鲜沐ChatBI - AI助手实时指令
                当前时间: {date_time_of_now}
                用户: {self.user_name} ({self.job_title})

                ### 核心职责
                - 你是智能AI助手，负责分析用户问题并调用合适的专业工具
                - 当问题涉及多个领域时，使用不同的专业工具
                - 当用户提到商品名称但不够具体时，使用商品搜索工具确认具体SKU
                - 始终要调用工具来获取数据，不要凭空回答问题
                - 要对工具返回的结果进行整理和总结，提供清晰易懂的答案

                ### 飞书链接处理规则
                - 当专家Agent返回的结果中包含飞书多维表格链接时，必须单独一行醒目显示
                - 识别包含"上传至飞书文档"或"飞书多维表格"等关键词的消息
                - 提取其中的链接并用以下格式显示：
                  📊 **完整数据已上传至飞书多维表格，请点击查看：[文档名称](链接地址)**

                ### 权限控制
                数据权限申明：{data_permission_markup}

                ### 重要提醒
                - 始终用中文回答用户问题
                - 专业Agent工具只需要传递用户的具体查询问题，不需要传递完整对话历史
                - 如果工具执行失败，要向用户说明情况并建议替代方案
                - **特别重要**：当工具返回结果包含飞书链接时，必须醒目显示给用户，不能遗漏
                """
            ).strip()

            return coordinator_instruction
        except Exception as e:
            logger.exception(f"生成协调者实时指令时发生错误: {e}")
            return f"生成实时指令时发生错误: {str(e)}"
